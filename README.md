# 多语言宠物博客站群系统

## 项目简介
专注于宠物知识分享的多语言博客站群系统，采用前后端分离架构，支持多域名多语言独立模板，具备完整的内容管理和SEO优化功能。

## 🚀 快速开始

### 环境要求
- Node.js >= 18.0.0
- MySQL >= 5.7.44
- Redis >= 6.0（可选，用于缓存）

### 安装依赖
```bash
# 安装前端依赖
cd frontend
npm install

# 安装后端依赖
cd ../backend
npm install
```

### 环境配置
```bash
# 复制环境配置文件
cp backend/.env.example backend/.env
# 编辑 .env 文件配置数据库等
```

### 数据库初始化
```bash
# 导入数据库结构
mysql -h ************ -u bengtai -p bengtai < docs/database/schema.sql
```

### 启动开发
```bash
# 启动后端服务
cd backend
npm run dev

# 启动前端服务（新终端）
cd frontend
npm run dev
```

## 📖 功能说明
- ✅ **多语言站群**：支持多域名独立语言模板
- ✅ **内容管理**：富文本编辑、分类管理、SEO优化
- ✅ **AI翻译**：一键翻译、人工校对工作流
- ✅ **评论系统**：多层嵌套回复、审核管理
- ✅ **SEO优化**：结构化数据、Sitemap、性能优化
- ✅ **广告集成**：Google Ads、Analytics独立配置

## 🛠️ 技术栈
- **前端**：Astro + TypeScript + Tailwind CSS
- **后端**：Node.js + Express + TypeScript
- **数据库**：MySQL 5.7.44
- **缓存**：Redis（可选）
- **部署**：Linux + 宝塔面板

## 📚 文档目录
- [系统架构设计](./docs/architecture.md)
- [数据库设计](./docs/database/design.md)
- [API接口文档](./docs/api.md)
- [前端开发指南](./docs/frontend-guide.md)
- [后端开发指南](./docs/backend-guide.md)
- [部署运维手册](./docs/deployment.md)
- [多语言模板指南](./docs/multilang-guide.md)
- [测试计划](./docs/testing.md)
- [开发进度计划](./docs/development-plan.md)

## 🌍 支持语言
- 英语 (美国) - example.com
- 德语 (德国) - beispiel.de  
- 俄语 (俄罗斯) - primer.ru

## 📊 项目状态
- 🚧 **开发阶段**：架构设计和文档制作
- 📅 **预计完成**：7-10个工作日
- 👥 **开发模式**：AI辅助单人开发

## 🔧 开发环境
- **操作系统**：macOS
- **数据库**：远程MySQL (************)
- **本地测试**：支持多域名模拟

## 📝 更新日志
### 2025-01-30
- ✅ 项目初始化
- ✅ 完成架构设计文档
- ✅ 数据库结构设计
- 🚧 开始核心功能开发

## 🤝 贡献指南
本项目采用AI辅助开发模式，所有代码都经过严格的质量检查和测试。

## 📄 许可证
MIT License

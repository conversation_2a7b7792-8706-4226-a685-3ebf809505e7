-- 多语言宠物博客站群系统数据库结构
-- 数据库: bengtai
-- 字符集: utf8mb4_unicode_ci

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- 站点配置表
-- ----------------------------
DROP TABLE IF EXISTS `sites`;
CREATE TABLE `sites` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `domain` varchar(255) NOT NULL COMMENT '域名',
  `language` varchar(10) NOT NULL COMMENT '语言代码 (en, de, ru)',
  `name` varchar(255) NOT NULL COMMENT '站点名称',
  `description` text COMMENT '站点描述',
  `logo` varchar(500) COMMENT 'Logo图片路径',
  `favicon` varchar(500) COMMENT 'Favicon路径',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否启用',
  `seo_title` varchar(255) COMMENT 'SEO标题',
  `seo_description` text COMMENT 'SEO描述',
  `seo_keywords` text COMMENT 'SEO关键词',
  `google_analytics` varchar(50) COMMENT 'GA追踪代码',
  `google_ads_client` varchar(50) COMMENT 'Google Ads客户端ID',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_domain` (`domain`),
  KEY `idx_language` (`language`),
  KEY `idx_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- 分类表
-- ----------------------------
DROP TABLE IF EXISTS `categories`;
CREATE TABLE `categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `site_id` int(11) NOT NULL COMMENT '所属站点ID',
  `parent_id` int(11) DEFAULT NULL COMMENT '父分类ID',
  `name` varchar(255) NOT NULL COMMENT '分类名称',
  `slug` varchar(255) NOT NULL COMMENT 'URL别名',
  `description` text COMMENT '分类描述',
  `image` varchar(500) COMMENT '分类图片',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序权重',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否启用',
  `seo_title` varchar(255) COMMENT 'SEO标题',
  `seo_description` text COMMENT 'SEO描述',
  `seo_keywords` text COMMENT 'SEO关键词',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_site_slug` (`site_id`, `slug`),
  KEY `idx_parent` (`parent_id`),
  KEY `idx_active` (`is_active`),
  KEY `idx_sort` (`sort_order`),
  FOREIGN KEY (`site_id`) REFERENCES `sites` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`parent_id`) REFERENCES `categories` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- 文章表
-- ----------------------------
DROP TABLE IF EXISTS `articles`;
CREATE TABLE `articles` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `site_id` int(11) NOT NULL COMMENT '所属站点ID',
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `title` varchar(500) NOT NULL COMMENT '文章标题',
  `slug` varchar(500) NOT NULL COMMENT 'URL别名',
  `excerpt` text COMMENT '文章摘要',
  `content` longtext NOT NULL COMMENT '文章内容',
  `featured_image` varchar(500) COMMENT '特色图片',
  `status` enum('draft','published','archived') DEFAULT 'draft' COMMENT '状态',
  `is_featured` tinyint(1) DEFAULT 0 COMMENT '是否推荐',
  `view_count` int(11) DEFAULT 0 COMMENT '浏览次数',
  `comment_count` int(11) DEFAULT 0 COMMENT '评论数量',
  `published_at` timestamp NULL COMMENT '发布时间',
  `seo_title` varchar(255) COMMENT 'SEO标题',
  `seo_description` text COMMENT 'SEO描述',
  `seo_keywords` text COMMENT 'SEO关键词',
  `structured_data` json COMMENT '结构化数据',
  `original_article_id` int(11) DEFAULT NULL COMMENT '原文文章ID（用于翻译关联）',
  `translation_status` enum('none','pending','completed') DEFAULT 'none' COMMENT '翻译状态',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_site_slug` (`site_id`, `slug`),
  KEY `idx_category` (`category_id`),
  KEY `idx_status` (`status`),
  KEY `idx_featured` (`is_featured`),
  KEY `idx_published` (`published_at`),
  KEY `idx_original` (`original_article_id`),
  KEY `idx_translation_status` (`translation_status`),
  KEY `idx_list_query` (`site_id`, `status`, `published_at`),
  KEY `idx_category_query` (`category_id`, `status`, `published_at`),
  FULLTEXT KEY `ft_content` (`title`, `content`),
  FULLTEXT KEY `ft_search` (`title`, `content`, `excerpt`),
  FOREIGN KEY (`site_id`) REFERENCES `sites` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE RESTRICT,
  FOREIGN KEY (`original_article_id`) REFERENCES `articles` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- 标签表
-- ----------------------------
DROP TABLE IF EXISTS `tags`;
CREATE TABLE `tags` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `site_id` int(11) NOT NULL COMMENT '所属站点ID',
  `name` varchar(255) NOT NULL COMMENT '标签名称',
  `slug` varchar(255) NOT NULL COMMENT 'URL别名',
  `description` text COMMENT '标签描述',
  `color` varchar(7) DEFAULT '#3B82F6' COMMENT '标签颜色',
  `usage_count` int(11) DEFAULT 0 COMMENT '使用次数',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_site_slug` (`site_id`, `slug`),
  KEY `idx_usage` (`usage_count`),
  FOREIGN KEY (`site_id`) REFERENCES `sites` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- 文章标签关联表
-- ----------------------------
DROP TABLE IF EXISTS `article_tags`;
CREATE TABLE `article_tags` (
  `article_id` int(11) NOT NULL,
  `tag_id` int(11) NOT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`article_id`, `tag_id`),
  KEY `idx_tag` (`tag_id`),
  FOREIGN KEY (`article_id`) REFERENCES `articles` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`tag_id`) REFERENCES `tags` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- 评论表
-- ----------------------------
DROP TABLE IF EXISTS `comments`;
CREATE TABLE `comments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `article_id` int(11) NOT NULL COMMENT '文章ID',
  `parent_id` int(11) DEFAULT NULL COMMENT '父评论ID',
  `author_name` varchar(255) NOT NULL COMMENT '评论者姓名',
  `author_email` varchar(255) NOT NULL COMMENT '评论者邮箱',
  `author_website` varchar(500) COMMENT '评论者网站',
  `author_ip` varchar(45) NOT NULL COMMENT '评论者IP',
  `content` text NOT NULL COMMENT '评论内容',
  `status` enum('pending','approved','rejected','spam') DEFAULT 'pending' COMMENT '审核状态',
  `user_agent` text COMMENT '用户代理',
  `reply_count` int(11) DEFAULT 0 COMMENT '回复数量',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_article` (`article_id`),
  KEY `idx_parent` (`parent_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created` (`created_at`),
  KEY `idx_email` (`author_email`),
  KEY `idx_article_status` (`article_id`, `status`, `created_at`),
  FOREIGN KEY (`article_id`) REFERENCES `articles` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`parent_id`) REFERENCES `comments` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- 管理员表
-- ----------------------------
DROP TABLE IF EXISTS `admins`;
CREATE TABLE `admins` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `email` varchar(255) NOT NULL COMMENT '邮箱',
  `password_hash` varchar(255) NOT NULL COMMENT '密码哈希',
  `name` varchar(255) NOT NULL COMMENT '真实姓名',
  `avatar` varchar(500) COMMENT '头像',
  `role` enum('super_admin','admin','editor') DEFAULT 'admin' COMMENT '角色',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否启用',
  `last_login_at` timestamp NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(45) COMMENT '最后登录IP',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`),
  UNIQUE KEY `uk_email` (`email`),
  KEY `idx_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- 翻译任务表
-- ----------------------------
DROP TABLE IF EXISTS `translation_tasks`;
CREATE TABLE `translation_tasks` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `original_article_id` int(11) NOT NULL COMMENT '原文文章ID',
  `target_site_id` int(11) NOT NULL COMMENT '目标站点ID',
  `target_language` varchar(10) NOT NULL COMMENT '目标语言',
  `translated_title` varchar(500) COMMENT '翻译后标题',
  `translated_content` longtext COMMENT '翻译后内容',
  `translated_excerpt` text COMMENT '翻译后摘要',
  `status` enum('pending','translating','review','completed','failed') DEFAULT 'pending' COMMENT '任务状态',
  `ai_model` varchar(50) COMMENT '使用的AI模型',
  `translation_prompt` text COMMENT '翻译提示词',
  `error_message` text COMMENT '错误信息',
  `reviewed_by` int(11) COMMENT '审核人ID',
  `reviewed_at` timestamp NULL COMMENT '审核时间',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_original` (`original_article_id`),
  KEY `idx_target_site` (`target_site_id`),
  KEY `idx_status` (`status`),
  KEY `idx_reviewer` (`reviewed_by`),
  FOREIGN KEY (`original_article_id`) REFERENCES `articles` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`target_site_id`) REFERENCES `sites` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`reviewed_by`) REFERENCES `admins` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- 系统配置表
-- ----------------------------
DROP TABLE IF EXISTS `system_configs`;
CREATE TABLE `system_configs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `config_key` varchar(255) NOT NULL COMMENT '配置键',
  `config_value` text COMMENT '配置值',
  `config_type` enum('string','number','boolean','json') DEFAULT 'string' COMMENT '配置类型',
  `description` text COMMENT '配置说明',
  `is_public` tinyint(1) DEFAULT 0 COMMENT '是否公开（前端可访问）',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- 操作日志表
-- ----------------------------
DROP TABLE IF EXISTS `admin_logs`;
CREATE TABLE `admin_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `admin_id` int(11) NOT NULL COMMENT '管理员ID',
  `action` varchar(255) NOT NULL COMMENT '操作类型',
  `resource_type` varchar(100) COMMENT '资源类型',
  `resource_id` int(11) COMMENT '资源ID',
  `description` text COMMENT '操作描述',
  `ip_address` varchar(45) NOT NULL COMMENT 'IP地址',
  `user_agent` text COMMENT '用户代理',
  `request_data` json COMMENT '请求数据',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_admin` (`admin_id`),
  KEY `idx_action` (`action`),
  KEY `idx_resource` (`resource_type`, `resource_id`),
  KEY `idx_created` (`created_at`),
  FOREIGN KEY (`admin_id`) REFERENCES `admins` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

SET FOREIGN_KEY_CHECKS = 1;

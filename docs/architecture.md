# 系统架构设计文档

## 1. 总体架构

### 1.1 架构概述
本系统采用前后端分离的微服务架构，支持多语言多域名的博客站群。

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   用户浏览器     │    │   CDN/反向代理   │    │   前端服务器     │
│                │────│                │────│   (Astro SSR)   │
│ example.com     │    │   Nginx/宝塔     │    │                │
│ beispiel.de     │    │                │    │                │
│ primer.ru       │    │                │    │                │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                       │
                                                       │ API调用
                                                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   管理后台       │    │   后端API服务    │    │   数据库服务     │
│   (Admin)       │────│  (Node.js)     │────│   (MySQL)      │
│                │    │                │    │                │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                │ 外部服务调用
                                ▼
                       ┌─────────────────┐
                       │   外部服务       │
                       │ - AI翻译API     │
                       │ - 图片存储      │
                       │ - 邮件服务      │
                       └─────────────────┘
```

### 1.2 技术选型说明

#### 前端技术栈
- **Astro**: 现代静态站点生成器，天然支持SSR/SSG，SEO友好
- **TypeScript**: 类型安全，提高代码质量
- **Tailwind CSS**: 原子化CSS，快速构建响应式界面
- **Alpine.js**: 轻量级交互框架，与Astro完美集成

#### 后端技术栈
- **Node.js + Express**: 成熟稳定，与前端技术栈统一
- **TypeScript**: 全栈类型安全
- **Prisma**: 现代ORM，类型安全的数据库操作
- **JWT**: 无状态身份认证
- **Multer**: 文件上传处理

#### 数据库设计
- **MySQL 5.7.44**: 关系型数据库，支持复杂查询
- **Redis**: 缓存层，提升性能（可选）

## 2. 核心模块设计

### 2.1 多语言路由模块
```typescript
// 域名到语言的映射
interface DomainLanguageMap {
  domain: string;
  language: string;
  template: string;
  isDefault: boolean;
}

// 路由解析器
class MultiLanguageRouter {
  async resolveLanguage(host: string): Promise<LanguageConfig>
  async getTemplate(language: string): Promise<TemplateConfig>
}
```

### 2.2 内容管理模块
```typescript
// 文章实体
interface Article {
  id: string;
  title: string;
  content: string;
  language: string;
  category: Category;
  seoMeta: SEOMeta;
  status: 'draft' | 'published' | 'archived';
  translations: Translation[];
}

// 翻译工作流
class TranslationWorkflow {
  async translateArticle(articleId: string, targetLanguages: string[]): Promise<Translation[]>
  async reviewTranslation(translationId: string, content: string): Promise<void>
  async publishTranslation(translationId: string): Promise<void>
}
```

### 2.3 SEO优化模块
```typescript
// SEO元数据管理
interface SEOMeta {
  title: string;
  description: string;
  keywords: string[];
  ogImage?: string;
  structuredData: StructuredData;
}

// 结构化数据生成器
class StructuredDataGenerator {
  generateArticleSchema(article: Article): object
  generateBreadcrumbSchema(path: string[]): object
  generateWebsiteSchema(siteConfig: SiteConfig): object
}
```

## 3. 数据流设计

### 3.1 用户访问流程
1. 用户访问域名 → Nginx解析 → 前端服务器
2. 前端服务器根据域名识别语言 → 加载对应模板
3. 服务端渲染页面 → 调用后端API获取数据
4. 返回完整HTML页面给用户

### 3.2 内容发布流程
1. 管理员创建中文原文 → 保存为草稿
2. 触发AI翻译 → 生成多语言版本（草稿状态）
3. 人工校对翻译内容 → 修改并保存
4. 发布到对应语言站点 → 更新缓存和Sitemap

### 3.3 评论处理流程
1. 用户提交评论 → 前端验证 → 后端API
2. 保存到数据库（待审核状态）
3. 管理员审核 → 批准/拒绝
4. 前端实时更新评论列表

## 4. 安全设计

### 4.1 身份认证
- JWT Token认证
- 刷新Token机制
- 密码加密存储（bcrypt）

### 4.2 数据安全
- SQL注入防护（参数化查询）
- XSS防护（内容过滤）
- CSRF防护（Token验证）
- 文件上传安全检查

### 4.3 API安全
- 请求频率限制
- IP白名单（管理后台）
- HTTPS强制跳转
- 敏感操作日志记录

## 5. 性能优化

### 5.1 前端优化
- 静态资源CDN加速
- 图片懒加载和压缩
- CSS/JS代码分割
- 关键资源预加载

### 5.2 后端优化
- Redis缓存热点数据
- 数据库查询优化
- API响应压缩
- 连接池管理

### 5.3 数据库优化
- 合理的索引设计
- 查询语句优化
- 读写分离（扩展方案）
- 定期数据清理

## 6. 部署架构

### 6.1 服务器配置
```
宝塔面板环境：
├── Nginx (反向代理 + 静态文件服务)
├── Node.js (后端API服务)
├── MySQL (数据库服务)
├── Redis (缓存服务，可选)
└── PM2 (进程管理)
```

### 6.2 域名配置
- 主域名：example.com (英语)
- 德语站：beispiel.de
- 俄语站：primer.ru
- 管理后台：admin.example.com

### 6.3 SSL证书
- 自动申请Let's Encrypt证书
- 强制HTTPS跳转
- HSTS安全头设置

## 7. 监控与日志

### 7.1 应用监控
- PM2进程监控
- API响应时间监控
- 错误率统计
- 资源使用监控

### 7.2 日志管理
- 访问日志（Nginx）
- 应用日志（Winston）
- 错误日志收集
- 日志轮转和清理

## 8. 扩展性设计

### 8.1 水平扩展
- 无状态API设计
- 负载均衡支持
- 数据库读写分离
- 缓存集群

### 8.2 功能扩展
- 插件化架构
- 主题模板系统
- 第三方集成接口
- 微服务拆分准备

## 9. 开发环境

### 9.1 本地开发
- Docker容器化（可选）
- 热重载开发服务器
- 多域名本地测试
- 数据库迁移工具

### 9.2 版本控制
- Git工作流规范
- 分支管理策略
- 代码审查流程
- 自动化测试集成

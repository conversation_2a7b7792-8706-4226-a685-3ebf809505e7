# 数据库设计文档

## 1. 数据库概述

### 1.1 设计原则
- 支持多语言内容存储
- 优化查询性能
- 保证数据一致性
- 便于扩展和维护

### 1.2 字符集配置
```sql
-- 数据库字符集：utf8mb4
-- 排序规则：utf8mb4_unicode_ci
-- 支持emoji和多语言字符
```

## 2. 核心数据表设计

### 2.1 站点配置表 (sites)
```sql
CREATE TABLE `sites` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `domain` varchar(255) NOT NULL COMMENT '域名',
  `language` varchar(10) NOT NULL COMMENT '语言代码 (en, de, ru)',
  `name` varchar(255) NOT NULL COMMENT '站点名称',
  `description` text COMMENT '站点描述',
  `logo` varchar(500) COMMENT 'Logo图片路径',
  `favicon` varchar(500) COMMENT 'Favicon路径',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否启用',
  `seo_title` varchar(255) COMMENT 'SEO标题',
  `seo_description` text COMMENT 'SEO描述',
  `seo_keywords` text COMMENT 'SEO关键词',
  `google_analytics` varchar(50) COMMENT 'GA追踪代码',
  `google_ads_client` varchar(50) COMMENT 'Google Ads客户端ID',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_domain` (`domain`),
  KEY `idx_language` (`language`),
  KEY `idx_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 2.2 分类表 (categories)
```sql
CREATE TABLE `categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `site_id` int(11) NOT NULL COMMENT '所属站点ID',
  `parent_id` int(11) DEFAULT NULL COMMENT '父分类ID',
  `name` varchar(255) NOT NULL COMMENT '分类名称',
  `slug` varchar(255) NOT NULL COMMENT 'URL别名',
  `description` text COMMENT '分类描述',
  `image` varchar(500) COMMENT '分类图片',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序权重',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否启用',
  `seo_title` varchar(255) COMMENT 'SEO标题',
  `seo_description` text COMMENT 'SEO描述',
  `seo_keywords` text COMMENT 'SEO关键词',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_site_slug` (`site_id`, `slug`),
  KEY `idx_parent` (`parent_id`),
  KEY `idx_active` (`is_active`),
  KEY `idx_sort` (`sort_order`),
  FOREIGN KEY (`site_id`) REFERENCES `sites` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`parent_id`) REFERENCES `categories` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 2.3 文章表 (articles)
```sql
CREATE TABLE `articles` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `site_id` int(11) NOT NULL COMMENT '所属站点ID',
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `title` varchar(500) NOT NULL COMMENT '文章标题',
  `slug` varchar(500) NOT NULL COMMENT 'URL别名',
  `excerpt` text COMMENT '文章摘要',
  `content` longtext NOT NULL COMMENT '文章内容',
  `featured_image` varchar(500) COMMENT '特色图片',
  `status` enum('draft','published','archived') DEFAULT 'draft' COMMENT '状态',
  `is_featured` tinyint(1) DEFAULT 0 COMMENT '是否推荐',
  `view_count` int(11) DEFAULT 0 COMMENT '浏览次数',
  `comment_count` int(11) DEFAULT 0 COMMENT '评论数量',
  `published_at` timestamp NULL COMMENT '发布时间',
  `seo_title` varchar(255) COMMENT 'SEO标题',
  `seo_description` text COMMENT 'SEO描述',
  `seo_keywords` text COMMENT 'SEO关键词',
  `structured_data` json COMMENT '结构化数据',
  `original_article_id` int(11) DEFAULT NULL COMMENT '原文文章ID（用于翻译关联）',
  `translation_status` enum('none','pending','completed') DEFAULT 'none' COMMENT '翻译状态',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_site_slug` (`site_id`, `slug`),
  KEY `idx_category` (`category_id`),
  KEY `idx_status` (`status`),
  KEY `idx_featured` (`is_featured`),
  KEY `idx_published` (`published_at`),
  KEY `idx_original` (`original_article_id`),
  KEY `idx_translation_status` (`translation_status`),
  FULLTEXT KEY `ft_content` (`title`, `content`),
  FOREIGN KEY (`site_id`) REFERENCES `sites` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE RESTRICT,
  FOREIGN KEY (`original_article_id`) REFERENCES `articles` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 2.4 文章标签表 (tags)
```sql
CREATE TABLE `tags` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `site_id` int(11) NOT NULL COMMENT '所属站点ID',
  `name` varchar(255) NOT NULL COMMENT '标签名称',
  `slug` varchar(255) NOT NULL COMMENT 'URL别名',
  `description` text COMMENT '标签描述',
  `color` varchar(7) DEFAULT '#3B82F6' COMMENT '标签颜色',
  `usage_count` int(11) DEFAULT 0 COMMENT '使用次数',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_site_slug` (`site_id`, `slug`),
  KEY `idx_usage` (`usage_count`),
  FOREIGN KEY (`site_id`) REFERENCES `sites` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 2.5 文章标签关联表 (article_tags)
```sql
CREATE TABLE `article_tags` (
  `article_id` int(11) NOT NULL,
  `tag_id` int(11) NOT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`article_id`, `tag_id`),
  KEY `idx_tag` (`tag_id`),
  FOREIGN KEY (`article_id`) REFERENCES `articles` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`tag_id`) REFERENCES `tags` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 2.6 评论表 (comments)
```sql
CREATE TABLE `comments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `article_id` int(11) NOT NULL COMMENT '文章ID',
  `parent_id` int(11) DEFAULT NULL COMMENT '父评论ID',
  `author_name` varchar(255) NOT NULL COMMENT '评论者姓名',
  `author_email` varchar(255) NOT NULL COMMENT '评论者邮箱',
  `author_website` varchar(500) COMMENT '评论者网站',
  `author_ip` varchar(45) NOT NULL COMMENT '评论者IP',
  `content` text NOT NULL COMMENT '评论内容',
  `status` enum('pending','approved','rejected','spam') DEFAULT 'pending' COMMENT '审核状态',
  `user_agent` text COMMENT '用户代理',
  `reply_count` int(11) DEFAULT 0 COMMENT '回复数量',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_article` (`article_id`),
  KEY `idx_parent` (`parent_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created` (`created_at`),
  KEY `idx_email` (`author_email`),
  FOREIGN KEY (`article_id`) REFERENCES `articles` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`parent_id`) REFERENCES `comments` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 2.7 管理员表 (admins)
```sql
CREATE TABLE `admins` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `email` varchar(255) NOT NULL COMMENT '邮箱',
  `password_hash` varchar(255) NOT NULL COMMENT '密码哈希',
  `name` varchar(255) NOT NULL COMMENT '真实姓名',
  `avatar` varchar(500) COMMENT '头像',
  `role` enum('super_admin','admin','editor') DEFAULT 'admin' COMMENT '角色',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否启用',
  `last_login_at` timestamp NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(45) COMMENT '最后登录IP',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`),
  UNIQUE KEY `uk_email` (`email`),
  KEY `idx_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 2.8 翻译任务表 (translation_tasks)
```sql
CREATE TABLE `translation_tasks` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `original_article_id` int(11) NOT NULL COMMENT '原文文章ID',
  `target_site_id` int(11) NOT NULL COMMENT '目标站点ID',
  `target_language` varchar(10) NOT NULL COMMENT '目标语言',
  `translated_title` varchar(500) COMMENT '翻译后标题',
  `translated_content` longtext COMMENT '翻译后内容',
  `translated_excerpt` text COMMENT '翻译后摘要',
  `status` enum('pending','translating','review','completed','failed') DEFAULT 'pending' COMMENT '任务状态',
  `ai_model` varchar(50) COMMENT '使用的AI模型',
  `translation_prompt` text COMMENT '翻译提示词',
  `error_message` text COMMENT '错误信息',
  `reviewed_by` int(11) COMMENT '审核人ID',
  `reviewed_at` timestamp NULL COMMENT '审核时间',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_original` (`original_article_id`),
  KEY `idx_target_site` (`target_site_id`),
  KEY `idx_status` (`status`),
  KEY `idx_reviewer` (`reviewed_by`),
  FOREIGN KEY (`original_article_id`) REFERENCES `articles` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`target_site_id`) REFERENCES `sites` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`reviewed_by`) REFERENCES `admins` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 2.9 系统配置表 (system_configs)
```sql
CREATE TABLE `system_configs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `config_key` varchar(255) NOT NULL COMMENT '配置键',
  `config_value` text COMMENT '配置值',
  `config_type` enum('string','number','boolean','json') DEFAULT 'string' COMMENT '配置类型',
  `description` text COMMENT '配置说明',
  `is_public` tinyint(1) DEFAULT 0 COMMENT '是否公开（前端可访问）',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 2.10 操作日志表 (admin_logs)
```sql
CREATE TABLE `admin_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `admin_id` int(11) NOT NULL COMMENT '管理员ID',
  `action` varchar(255) NOT NULL COMMENT '操作类型',
  `resource_type` varchar(100) COMMENT '资源类型',
  `resource_id` int(11) COMMENT '资源ID',
  `description` text COMMENT '操作描述',
  `ip_address` varchar(45) NOT NULL COMMENT 'IP地址',
  `user_agent` text COMMENT '用户代理',
  `request_data` json COMMENT '请求数据',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_admin` (`admin_id`),
  KEY `idx_action` (`action`),
  KEY `idx_resource` (`resource_type`, `resource_id`),
  KEY `idx_created` (`created_at`),
  FOREIGN KEY (`admin_id`) REFERENCES `admins` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

## 3. 索引优化策略

### 3.1 查询优化索引
```sql
-- 文章列表查询优化
ALTER TABLE articles ADD INDEX idx_list_query (site_id, status, published_at DESC);

-- 分类文章查询优化
ALTER TABLE articles ADD INDEX idx_category_query (category_id, status, published_at DESC);

-- 搜索优化
ALTER TABLE articles ADD FULLTEXT INDEX ft_search (title, content, excerpt);

-- 评论查询优化
ALTER TABLE comments ADD INDEX idx_article_status (article_id, status, created_at DESC);
```

### 3.2 性能监控
- 定期分析慢查询日志
- 监控索引使用情况
- 优化高频查询语句

## 4. 数据关系图

```
sites (站点)
├── categories (分类)
│   └── articles (文章)
│       ├── article_tags (文章标签关联)
│       │   └── tags (标签)
│       └── comments (评论)
├── translation_tasks (翻译任务)
└── admin_logs (操作日志)

admins (管理员)
├── translation_tasks (翻译任务审核)
└── admin_logs (操作日志)

system_configs (系统配置)
```

## 5. 数据初始化

### 5.1 默认站点数据
```sql
-- 插入默认站点配置
INSERT INTO sites (domain, language, name, description) VALUES
('example.com', 'en', 'Pet Knowledge Hub', 'Your trusted source for pet care information'),
('beispiel.de', 'de', 'Haustier Wissen', 'Ihre vertrauenswürdige Quelle für Haustierpflege'),
('primer.ru', 'ru', 'Знания о питомцах', 'Ваш надежный источник информации по уходу за питомцами');
```

### 5.2 默认分类数据
```sql
-- 英语站点分类
INSERT INTO categories (site_id, name, slug, description) VALUES
(1, 'Cat Care', 'cat-care', 'Everything about caring for cats'),
(1, 'Dog Care', 'dog-care', 'Everything about caring for dogs');

-- 德语站点分类
INSERT INTO categories (site_id, name, slug, description) VALUES
(2, 'Katzenpflege', 'katzenpflege', 'Alles über die Pflege von Katzen'),
(2, 'Hundepflege', 'hundepflege', 'Alles über die Pflege von Hunden');

-- 俄语站点分类
INSERT INTO categories (site_id, name, slug, description) VALUES
(3, 'Уход за кошками', 'uhod-za-koshkami', 'Все о уходе за кошками'),
(3, 'Уход за собаками', 'uhod-za-sobakami', 'Все о уходе за собаками');
```

### 5.3 系统配置初始化
```sql
INSERT INTO system_configs (config_key, config_value, config_type, description) VALUES
('ai_api_url', 'https://ai.wanderintree.top', 'string', 'AI翻译API地址'),
('ai_api_key', 'sk-SMXjycC5GnJRswpJB8Ef6f632d794bBa9a1bAbB828E7Ee9d', 'string', 'AI翻译API密钥'),
('ai_model', 'gemini-2.5-pro', 'string', '默认AI模型'),
('site_cache_ttl', '3600', 'number', '站点缓存时间（秒）'),
('comment_auto_approve', 'false', 'boolean', '评论自动审核'),
('max_upload_size', '10485760', 'number', '最大上传文件大小（字节）');
```
